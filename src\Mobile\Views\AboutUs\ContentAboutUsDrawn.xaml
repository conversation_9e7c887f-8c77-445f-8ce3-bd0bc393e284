<?xml version="1.0" encoding="UTF-8" ?>
<pages:IncludedContent
    x:Class="AppoMobi.ContentAboutUsDrawn"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:nifty="clr-namespace:AppoMobi.Nifty"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:views="clr-namespace:AppoMobi.Mobile.Views"
    xmlns:xam="clr-namespace:AppoMobi.Xam">

    <draw:Canvas
        Gestures="Lock"
        HorizontalOptions="Fill"
        RenderingMode="Accelerated"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            Margin="2,0,2,0"
            Spacing="0"
            Type="Column"
            VerticalOptions="Fill">

            <!--  CARD 1  -->
            <draw:SkiaShape
                BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
                CornerRadius="8"
                Margin="4"
                Type="Rectangle">

                <draw:SkiaLayout
                    Margin="0,5,0,0"
                    Spacing="0"
                    Type="Column">

                    <!--  image  -->
                    <draw:SkiaImage 
                        x:Name="imgLogo" 
                        Margin="4,4,4,4"
                        Source="logoabout.jpg" />

                    <!--  Icons Row - Custom implementation  -->
                    <draw:SkiaLayout
                        x:Name="IconsList"
                        Margin="12,0,12,0"
                        BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
                        HorizontalOptions="Start"
                        Type="Row"
                        Spacing="8">
                        <!--  Icons will be added programmatically  -->
                    </draw:SkiaLayout>

                    <!--  Description Label  -->
                    <draw:SkiaLabel
                        x:Name="cCollapsedLabel"
                        Margin="12,0,12,8"
                        Text="{Binding Info.Strings.AboutCompanyDesc}"
                        TextColor="{x:Static appoMobi:AppColors.CardsHeaderText}"
                        LineBreakMode="WordWrap"
                        MaxLines="999999" />

                </draw:SkiaLayout>
            </draw:SkiaShape>

            <!--  Contact Us Card  -->
            <draw:SkiaShape
                x:Name="cardContact"
                BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
                CornerRadius="8"
                IsVisible="False"
                Margin="6"
                Padding="0,0,0,8"
                Type="Rectangle">

                <draw:SkiaLayout 
                    Spacing="0"
                    Type="Column">

                    <draw:SkiaLabel
                        Margin="2,8,0,8"
                        InputTransparent="True"
                        Text="{x:Static resX:ResStrings.OurContacts}"
                        TextColor="{x:Static appoMobi:AppColors.CardsHeaderText}"
                        FontSize="16"
                        FontAttributes="Bold" />

                    <!--  Header Divider  -->
                    <draw:SkiaShape
                        BackgroundColor="{x:Static appoMobi:AppColors.CardsHeaderText}"
                        HeightRequest="1"
                        HorizontalOptions="Fill"
                        Margin="2,0,2,8"
                        Opacity="0.3"
                        Type="Rectangle" />

                    <!--  Contact List - Custom implementation  -->
                    <draw:SkiaLayout
                        x:Name="NiftyList"
                        BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
                        Type="Column"
                        Spacing="4">
                        <!--  Contact items will be added programmatically  -->
                    </draw:SkiaLayout>

                </draw:SkiaLayout>
            </draw:SkiaShape>

            <!--  See On Map Card  -->
            <draw:SkiaShape
                x:Name="cardMap"
                BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
                CornerRadius="8"
                IsVisible="False"
                Margin="6"
                Padding="0,0,0,8"
                Type="Rectangle">

                <draw:SkiaLayout 
                    Spacing="0"
                    Type="Column">

                    <draw:SkiaHotspot Tapped="ShowMapTapped">
                        <draw:SkiaLabel
                            Margin="2,8,0,8"
                            Text="{x:Static resX:ResStrings.ShowOnMap}"
                            TextColor="{x:Static appoMobi:AppColors.CardsHeaderText}"
                            FontSize="16"
                            FontAttributes="Bold" />
                    </draw:SkiaHotspot>

                    <!--  Header Divider  -->
                    <draw:SkiaShape
                        BackgroundColor="{x:Static appoMobi:AppColors.CardsHeaderText}"
                        HeightRequest="1"
                        HorizontalOptions="Fill"
                        Margin="2,0,2,8"
                        Opacity="0.3"
                        Type="Rectangle" />

                    <!--  Map List - Custom implementation  -->
                    <draw:SkiaLayout
                        x:Name="NiftyListMap"
                        Type="Column"
                        Spacing="4">
                        <!--  Map items will be added programmatically  -->
                    </draw:SkiaLayout>

                </draw:SkiaLayout>
            </draw:SkiaShape>

            <!--  Dev Footer  -->
            <draw:SkiaLayout 
                x:Name="DevFooter"
                Margin="6"
                HorizontalOptions="Fill">
                <!--  Dev footer content will be added programmatically  -->
            </draw:SkiaLayout>

        </draw:SkiaLayout>

    </draw:Canvas>

</pages:IncludedContent>
