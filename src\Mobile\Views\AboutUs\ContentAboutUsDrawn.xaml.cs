using System;
using System.Collections.Generic;
using AppoMobi.Common.Constants;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Common.Dto;
using AppoMobi.Nifty;
using AppoMobi.Pages;
using AppoMobi.Services;
using AppoMobi.Tenant;
using AppoMobi.UI;
using AppoMobi.Xam;
using DrawnUi.Draw;

namespace AppoMobi
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ContentAboutUsDrawn : IncludedContent
    {
        public List<CMyListItem> ContactList = new List<CMyListItem>();
        public List<CMyListItem> ContactListMap = new List<CMyListItem>();
        public List<CMyListItem> ContactListRes = new List<CMyListItem>();

        public string AppVersion { get; set; }

        private CompanyInfoDTO Model => Core.Current.MyCompany;

        public ContentAboutUsDrawn(IPageEnhancedNav daddy)
        {
            InitializeComponent();
            Init(daddy);

            BindingContext = Core.Current;

            string prms = null;

            // Set company logo
            imgLogo.Source = "logoabout.jpg";

            // Build contact list
            prms = Core.Current.MyCompany.ContactUsTel;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "tel",
                    FontIcon = FontIcons.fa_phone_square,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsEmail;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "email",
                    FontIcon = FontIcons.fa_envelope_square,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsWebsite;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "url",
                    FontIcon = FontIcons.fa_globe,
                    Desc = prms,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsFacebook;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "facebook",
                    FontIcon = FontIcons.fa_facebook,
                    Desc = ResStrings.Facebook,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsVk;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "vk",
                    FontIcon = FontIcons.fa_vk,
                    Desc = ResStrings.VK,
                    Parameters = prms,
                    Selected = false
                });
            }

            prms = Core.Current.MyCompany.ContactUsInstagram;
            if (!string.IsNullOrEmpty(prms))
            {
                ContactList.Add(new CMyListItem
                {
                    Tag = "instagram",
                    FontIcon = FontIcons.fa_instagram,
                    Desc = ResStrings.Instagram,
                    Parameters = prms,
                    Selected = false
                });
            }

            // Build map contact list
            ContactListMap.Add(new CMyListItem
            {
                Tag = "map",
                FontIcon = FontIcons.fa_map_marked_alt,
                Parameters = Core.Current.MyCompany.ContactUsAddress,
                Desc = Core.Current.MyCompany.ContactUsAddress,
                Selected = false
            });

            if (Core.Current.MyCompany.MapX > 0)
            {
                ContactListMap.Add(new CMyListItem
                {
                    Tag = "navi",
                    FontIcon = FontIcons.fa_route,
                    Parameters = Core.Current.MyCompany.ContactUsAddress,
                    Desc = ResStrings.HowToGet,
                    Selected = false
                });
            }

            if (!string.IsNullOrEmpty(Core.Current.MyCompany.ContactUsAddress))
            {
                cardMap.IsVisible = true;
            }
            else
            {
                cardMap.IsVisible = false;
            }

            // Setup toolbar icons
            if (!string.IsNullOrEmpty(Core.Current.MyCompany.ContactUsTel))
            {
                Daddy.RightIcon1Symbol.SetIcon(FontIcons.fa_phone);
                Daddy.RightIcon1Symbol.RotationY = 180;
                Daddy.ToggleButtonVisibility(ButtonType.Right1, true);
            }
            else
            {
                Daddy.ToggleButtonVisibility(ButtonType.Right1, false);
            }

            // Show contact card if we have contacts
            if (ContactList.Count > 0)
            {
                cardContact.IsVisible = true;
            }

            // Build the UI programmatically
            BuildIconsList();
            BuildContactList();
            BuildMapList();
            BuildDevFooter();

            AppVersion = ResStrings.PageSettings_PageSettings_Version + " " + Core.Native.GetAppVersion();
        }

        private void BuildIconsList()
        {
            IconsList.Children.Clear();
            
            foreach (var item in ContactList)
            {
                var iconButton = new SkiaHotspot
                {
                    Margin = new Thickness(4),
                    WidthRequest = 40,
                    HeightRequest = 40
                };

                var icon = new SkiaLabel
                {
                    Text = item.FontIcon,
                    FontFamily = "FontAwesome",
                    FontSize = 24,
                    TextColor = AppColors.CardsHeaderText,
                    HorizontalOptions = LayoutOptions.Center,
                    VerticalOptions = LayoutOptions.Center
                };

                iconButton.Children.Add(icon);
                iconButton.Tapped += (s, e) => OnIconTapped(item);
                
                IconsList.Children.Add(iconButton);
            }
        }

        private void BuildContactList()
        {
            NiftyList.Children.Clear();
            
            foreach (var item in ContactList)
            {
                var contactItem = CreateContactListItem(item);
                NiftyList.Children.Add(contactItem);
            }
        }

        private void BuildMapList()
        {
            NiftyListMap.Children.Clear();
            
            foreach (var item in ContactListMap)
            {
                var mapItem = CreateContactListItem(item);
                NiftyListMap.Children.Add(mapItem);
            }
        }

        private SkiaHotspot CreateContactListItem(CMyListItem item)
        {
            var hotspot = new SkiaHotspot
            {
                Margin = new Thickness(2),
                Padding = new Thickness(8, 4)
            };

            var layout = new SkiaLayout
            {
                Type = LayoutType.Row,
                Spacing = 12,
                HorizontalOptions = LayoutOptions.Fill
            };

            var icon = new SkiaLabel
            {
                Text = item.FontIcon,
                FontFamily = "FontAwesome",
                FontSize = 16,
                TextColor = AppColors.CardsHeaderText,
                VerticalOptions = LayoutOptions.Center,
                WidthRequest = 24
            };

            var text = new SkiaLabel
            {
                Text = item.Desc,
                FontSize = 14,
                TextColor = AppColors.CardsHeaderText,
                VerticalOptions = LayoutOptions.Center,
                HorizontalOptions = LayoutOptions.Fill
            };

            layout.Children.Add(icon);
            layout.Children.Add(text);
            hotspot.Children.Add(layout);
            
            hotspot.Tapped += (s, e) => OnContactItemTapped(item);
            
            return hotspot;
        }

        private void BuildDevFooter()
        {
            var versionLabel = new SkiaLabel
            {
                Text = AppVersion,
                FontSize = 12,
                TextColor = AppColors.CardsHeaderText,
                HorizontalOptions = LayoutOptions.Center,
                Opacity = 0.7
            };
            
            DevFooter.Children.Add(versionLabel);
        }

        public override void OnRightIcon1Clicked()
        {
            CWorld.Dial(Core.Current.MyCompany.ContactUsTel);
            base.OnRightIcon1Clicked();
        }

        private void OnIconTapped(CMyListItem item)
        {
            HandleContactAction(item);
        }

        private void OnContactItemTapped(CMyListItem item)
        {
            HandleContactAction(item);
        }

        // Legacy method for compatibility with original interface
        private async void NiftyList_OnTapped(object sender, EventArgs e)
        {
            // This method is kept for compatibility but the actual handling
            // is done through the individual item tap handlers
        }

        private async void HandleContactAction(CMyListItem item)
        {
            switch (item.Tag)
            {
                case "tel":
                    CWorld.Dial(item.Parameters);
                    break;
                case "email":
                case "mail":
                    Core.Native.OpenUrl($"mailto:{item.Parameters}");
                    break;
                case "url":
                    Core.Native.OpenUrl(item.Parameters);
                    break;
                case "facebook":
                    if (!Core.Native.OpenInAppFacebook(Core.Current.MyCompany.ContactUsFacebookNumeric))
                    {
                        Core.Native.OpenUrl(@"https://www.facebook.com/" + Core.Current.MyCompany.ContactUsFacebook);
                    }
                    break;
                case "vk":
                    Core.Native.OpenUrl(@"https://www.vk.com/" + Core.Current.MyCompany.ContactUsVk);
                    break;
                case "instagram":
                    if (!Core.Native.OpenInAppInstagram(Core.Current.MyCompany.ContactUsInstagram))
                    {
                        Core.Native.OpenUrl(@"https://www.instagram.com/" + Core.Current.MyCompany.ContactUsInstagram);
                    }
                    break;
                case "map":
                    var mapUrl = $"https://maps.google.com/?q={Uri.EscapeDataString(item.Parameters)}";
                    Core.Native.OpenUrl(mapUrl);
                    break;
                case "navi":
                    // Handle navigation - could implement native navigation here
                    // await NiftyNative.Navigate(Core.Current.MyCompany.Name, Core.Current.MyCompany.MapX, Core.Current.MyCompany.MapY);
                    break;
            }
        }

        private void ShowMapTapped(object sender, EventArgs e)
        {
            // Handle show on map action
            if (!string.IsNullOrEmpty(Core.Current.MyCompany.ContactUsAddress))
            {
                // Open map with address
                var address = Core.Current.MyCompany.ContactUsAddress;
                var mapUrl = $"https://maps.google.com/?q={Uri.EscapeDataString(address)}";
                Core.Native.OpenUrl(mapUrl);
            }
        }
    }
}
